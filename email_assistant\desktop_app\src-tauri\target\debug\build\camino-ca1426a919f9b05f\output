cargo:rerun-if-changed=build.rs
cargo:rustc-check-cfg=cfg(doc_cfg)
cargo:rustc-check-cfg=cfg(path_buf_deref_mut)
cargo:rustc-check-cfg=cfg(path_buf_capacity)
cargo:rustc-check-cfg=cfg(shrink_to)
cargo:rustc-check-cfg=cfg(try_reserve_2)
cargo:rustc-check-cfg=cfg(os_str_bytes)
cargo:rustc-check-cfg=cfg(absolute_path)
cargo:rustc-cfg=path_buf_capacity
cargo:rustc-cfg=shrink_to
cargo:rustc-cfg=try_reserve_2
cargo:rustc-cfg=path_buf_deref_mut
cargo:rustc-cfg=os_str_bytes
cargo:rustc-cfg=absolute_path
