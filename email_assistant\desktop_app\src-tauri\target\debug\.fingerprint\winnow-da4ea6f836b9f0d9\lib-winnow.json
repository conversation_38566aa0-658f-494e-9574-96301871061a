{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 9076258246958265911, "path": 1373434048617311058, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\winnow-da4ea6f836b9f0d9\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}