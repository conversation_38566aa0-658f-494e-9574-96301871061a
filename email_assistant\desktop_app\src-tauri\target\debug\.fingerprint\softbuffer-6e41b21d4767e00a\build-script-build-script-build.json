{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 8750083498156175927, "deps": [[1884099982326826527, "cfg_aliases", false, 1944085892474097618]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-6e41b21d4767e00a\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}